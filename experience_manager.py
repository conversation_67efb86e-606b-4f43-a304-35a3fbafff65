import json
import os
import time
import hashlib
import logging
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import requests


@dataclass
class QueryExperience:
    """查询经验数据结构"""
    query: str
    query_hash: str
    final_answer: str
    execution_time: float
    token_usage: int
    tools_used: List[str]
    success: bool
    timestamp: str
    embedding: Optional[List[float]] = None
    metadata: Optional[Dict[str, Any]] = None


class ExperienceManager:
    """经验管理器 - 保存和检索成功的查询经验"""
    
    def __init__(self, config_path: str = "experience_config.json"):
        """初始化经验管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.experience_config = self.config["experience_manager"]
        self.embedding_config = self.config["embedding_config"]
        self.text_similarity_config = self.config["text_similarity_config"]
        
        # 设置日志
        self._setup_logging()
        
        # 经验存储文件路径
        self.experience_file = self.experience_config["experience_file"]
        
        # 加载现有经验
        self.experiences: List[QueryExperience] = self._load_experiences()
        
        self.logger.info(f"经验管理器初始化完成，已加载 {len(self.experiences)} 条经验")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件未找到: {config_path}")
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _setup_logging(self):
        """设置日志"""
        log_config = self.config["logging_config"]
        
        # 创建logger
        self.logger = logging.getLogger("ExperienceManager")
        self.logger.setLevel(getattr(logging, log_config["log_level"]))
        
        # 创建文件处理器
        handler = logging.FileHandler(
            log_config["log_file"], 
            encoding='utf-8'
        )
        
        # 设置格式
        formatter = logging.Formatter(log_config["log_format"])
        handler.setFormatter(formatter)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self.logger.addHandler(handler)
    
    def _load_experiences(self) -> List[QueryExperience]:
        """从文件加载经验数据"""
        if not os.path.exists(self.experience_file):
            return []
        
        try:
            with open(self.experience_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                experiences = []
                for item in data:
                    experiences.append(QueryExperience(**item))
                return experiences
        except Exception as e:
            self.logger.error(f"加载经验文件失败: {e}")
            return []
    
    def _save_experiences(self):
        """保存经验数据到文件"""
        try:
            # 限制经验数量
            max_experiences = self.experience_config["max_experiences"]
            if len(self.experiences) > max_experiences:
                # 保留最新的经验
                self.experiences = self.experiences[-max_experiences:]
            
            # 转换为字典格式保存
            data = [asdict(exp) for exp in self.experiences]
            
            with open(self.experience_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            self.logger.info(f"已保存 {len(self.experiences)} 条经验到文件")
            
        except Exception as e:
            self.logger.error(f"保存经验文件失败: {e}")
    
    def _generate_query_hash(self, query: str) -> str:
        """生成查询的哈希值"""
        return hashlib.md5(query.encode('utf-8')).hexdigest()
    
    async def _get_embedding(self, text: str) -> Optional[List[float]]:
        """获取文本的embedding向量"""
        if not self.experience_config["use_embedding"]:
            return None
        
        try:
            # 使用配置的embedding服务
            headers = {
                "Authorization": f"Bearer {self.embedding_config['api_key']}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.embedding_config["model"],
                "input": text
            }
            
            response = requests.post(
                self.embedding_config["base_url"],
                headers=headers,
                json=data,
                timeout=self.embedding_config["timeout"]
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["data"][0]["embedding"]
            else:
                self.logger.error(f"获取embedding失败: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"获取embedding异常: {e}")
            return None
    
    def _calculate_text_similarity(self, query1: str, query2: str) -> float:
        """计算文本相似度（基于字符和长度）"""
        config = self.text_similarity_config
        
        # Jaccard相似度
        set1 = set(query1.lower())
        set2 = set(query2.lower())
        jaccard = len(set1 & set2) / len(set1 | set2) if set1 | set2 else 0
        
        # 字符相似度
        common_chars = sum(1 for c in query1.lower() if c in query2.lower())
        char_sim = common_chars / max(len(query1), len(query2)) if max(len(query1), len(query2)) > 0 else 0
        
        # 长度相似度
        len_sim = 1 - abs(len(query1) - len(query2)) / max(len(query1), len(query2)) if max(len(query1), len(query2)) > 0 else 1
        
        # 加权计算
        similarity = (
            jaccard * config["jaccard_weight"] +
            char_sim * config["char_weight"] +
            len_sim * config["length_weight"]
        )
        
        return similarity
    
    def _calculate_embedding_similarity(self, embedding1: List[float], embedding2: List[float]) -> float:
        """计算embedding向量相似度"""
        try:
            vec1 = np.array(embedding1).reshape(1, -1)
            vec2 = np.array(embedding2).reshape(1, -1)
            return cosine_similarity(vec1, vec2)[0][0]
        except Exception as e:
            self.logger.error(f"计算embedding相似度失败: {e}")
            return 0.0

    async def save_experience(
        self,
        query: str,
        final_answer: str,
        execution_time: float,
        token_usage: int,
        tools_used: List[str],
        success: bool = True,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """保存查询经验

        Args:
            query: 用户查询
            final_answer: 最终答案
            execution_time: 执行时间（秒）
            token_usage: token使用量
            tools_used: 使用的工具列表
            success: 是否成功
            metadata: 额外的元数据

        Returns:
            是否保存成功
        """
        if not self.experience_config["enabled"]:
            return False

        # 检查是否满足保存条件
        if execution_time < self.experience_config["min_execution_time"]:
            self.logger.info(f"执行时间过短，跳过保存: {execution_time}s < {self.experience_config['min_execution_time']}s")
            print(f"[EXPERIENCE] 执行时间过短，跳过保存: {execution_time}s < {self.experience_config['min_execution_time']}s")
            return False

        if token_usage < self.experience_config["min_token_usage"]:
            self.logger.info(f"token使用量过少，跳过保存: {token_usage} < {self.experience_config['min_token_usage']}")
            print(f"[EXPERIENCE] token使用量过少，跳过保存: {token_usage} < {self.experience_config['min_token_usage']}")
            return False

        if not success:
            self.logger.info("查询失败，跳过保存")
            print(f"[EXPERIENCE] 查询失败，跳过保存")
            return False

        try:
            # 生成查询哈希
            query_hash = self._generate_query_hash(query)

            # 检查是否已存在相同的查询
            existing_exp = next((exp for exp in self.experiences if exp.query_hash == query_hash), None)
            if existing_exp:
                self.logger.info(f"查询已存在，跳过保存: {query[:50]}...")
                print(f"[EXPERIENCE] 查询已存在，跳过保存: {query[:50]}...")
                return False

            # 获取embedding（如果启用）
            embedding = None
            if self.experience_config["use_embedding"]:
                embedding = await self._get_embedding(query)

            # 创建经验对象
            experience = QueryExperience(
                query=query,
                query_hash=query_hash,
                final_answer=final_answer,
                execution_time=execution_time,
                token_usage=token_usage,
                tools_used=tools_used,
                success=success,
                timestamp=datetime.now().isoformat(),
                embedding=embedding,
                metadata=metadata or {}
            )

            # 添加到经验列表
            self.experiences.append(experience)

            # 自动保存（如果启用）
            if self.experience_config["auto_save"]:
                self._save_experiences()

            self.logger.info(f"成功保存查询经验: {query[:50]}...")
            print(f"[EXPERIENCE] 成功保存查询经验: {query[:50]}...")
            print(f"[EXPERIENCE] 经验详情: 执行时间={execution_time:.2f}s, token={token_usage}, 工具={tools_used}")
            return True

        except Exception as e:
            self.logger.error(f"保存经验失败: {e}")
            return False

    async def find_similar_experiences(self, query: str, top_k: Optional[int] = None) -> List[Tuple[QueryExperience, float]]:
        """查找相似的查询经验

        Args:
            query: 用户查询
            top_k: 返回的最大数量，默认使用配置中的值

        Returns:
            相似经验列表，每个元素为 (经验, 相似度分数)
        """
        if not self.experience_config["enabled"] or not self.experiences:
            return []

        if top_k is None:
            top_k = self.experience_config["top_k_similar"]

        try:
            similarities = []
            query_embedding = None

            # 获取查询的embedding（如果启用）
            if self.experience_config["use_embedding"]:
                query_embedding = await self._get_embedding(query)

            for experience in self.experiences:
                if not experience.success:
                    continue

                similarity = 0.0

                # 使用embedding计算相似度
                if (query_embedding and experience.embedding and
                    self.experience_config["use_embedding"]):
                    similarity = self._calculate_embedding_similarity(query_embedding, experience.embedding)
                else:
                    # 使用文本相似度
                    similarity = self._calculate_text_similarity(query, experience.query)

                # 检查相似度阈值
                if similarity >= self.experience_config["similarity_threshold"]:
                    similarities.append((experience, similarity))

            # 按相似度排序并返回top_k
            similarities.sort(key=lambda x: x[1], reverse=True)
            result = similarities[:top_k]

            self.logger.info(f"找到 {len(result)} 个相似经验，查询: {query[:50]}...")
            return result

        except Exception as e:
            self.logger.error(f"查找相似经验失败: {e}")
            return []

    def get_experience_summary(self) -> Dict[str, Any]:
        """获取经验统计摘要"""
        if not self.experiences:
            return {
                "total_experiences": 0,
                "success_rate": 0.0,
                "avg_execution_time": 0.0,
                "avg_token_usage": 0.0,
                "most_used_tools": []
            }

        successful_experiences = [exp for exp in self.experiences if exp.success]

        # 统计工具使用频率
        tool_usage = {}
        for exp in successful_experiences:
            for tool in exp.tools_used:
                tool_usage[tool] = tool_usage.get(tool, 0) + 1

        most_used_tools = sorted(tool_usage.items(), key=lambda x: x[1], reverse=True)[:5]

        return {
            "total_experiences": len(self.experiences),
            "successful_experiences": len(successful_experiences),
            "success_rate": len(successful_experiences) / len(self.experiences) if self.experiences else 0.0,
            "avg_execution_time": sum(exp.execution_time for exp in successful_experiences) / len(successful_experiences) if successful_experiences else 0.0,
            "avg_token_usage": sum(exp.token_usage for exp in successful_experiences) / len(successful_experiences) if successful_experiences else 0.0,
            "most_used_tools": most_used_tools
        }

    def manual_save(self):
        """手动保存经验到文件"""
        self._save_experiences()

    def clear_experiences(self):
        """清空所有经验"""
        self.experiences.clear()
        self._save_experiences()
        self.logger.info("已清空所有经验")

    def export_experiences(self, export_path: str) -> bool:
        """导出经验到指定文件

        Args:
            export_path: 导出文件路径

        Returns:
            是否导出成功
        """
        try:
            data = [asdict(exp) for exp in self.experiences]
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"成功导出 {len(self.experiences)} 条经验到 {export_path}")
            return True

        except Exception as e:
            self.logger.error(f"导出经验失败: {e}")
            return False
