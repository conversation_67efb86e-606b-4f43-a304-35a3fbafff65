#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于官方MCP SDK的统计数据查询服务 - FastMCP版本
提供统计表查询工具，支持指定时间范围、字段、过滤条件等参数
"""

import json
import traceback
import urllib.request
import time
import struct
import ssl
import random
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta

from mcp.server.fastmcp import FastMCP

# 创建FastMCP服务器实例
mcp = FastMCP("统计数据查询服务")

class StatsApiClient:
    def __init__(self, url: str, user: str, password: str, version: str = "6.1"):
        """
        初始化统计API客户端
        Args:
            url: API服务器URL
            user: 用户名
            password: 密码
            version: API版本
        """
        self.url = url
        self.user = user
        self.password = password
        self.ver = version
        self.session = ''
        
    def get_error_message(self, error_code: int) -> str:
        """
        将API错误码翻译为中文描述
        Args:
            error_code: API返回的错误码
        Returns:
            对应的中文错误描述
        """
        error_map = {
            0: '请求成功',
            1: '资源错误',
            2: '创建线程失败',
            3: '未知命令',
            4: '未实现',
            5: '需要登录',
            6: '没有权限',
            7: '太多请求',
            8: '用户取消',
            9: '参数错误',
            10: '连接已断开',
            11: '请求超时',
            13: '参数错误：查询字段不能解析',
            14: '参数错误：无效的查询表名称',
            15: '参数错误：无效的链路ID',
            16: '参数错误：无效的查询时间',
            17: '参数错误：无效的时间单位',
            18: '参数错误：无效的排序类型',
            19: '参数错误：无效的时间周期',
            20: '参数错误：无效的配置类型',
            21: '查询表不存在',
            22: '查询字段不存在',
            23: '请求失败',
            24: '系统繁忙',
            25: '错误的URL',
            26: '参数错误',
            255: '其他错误',
        }
        
        if error_code in error_map:
            return error_map[error_code]
        else:
            return f'无效错误码: {error_code}'
        
    def strtime_to_inttime(self, strtime: str) -> int:
        """字符串时间转换为时间戳，支持多种时间格式"""
        # 支持的时间格式列表
        time_formats = [
            '%Y-%m-%d %H:%M:%S',  # 完整格式：2025-06-23 10:30:00
            '%Y-%m-%d',           # 日期格式：2025-06-23 (自动补充00:00:00)
            '%Y/%m/%d %H:%M:%S',  # 斜杠格式：2025/06/23 10:30:00
            '%Y/%m/%d',           # 斜杠日期：2025/06/23
        ]
        
        for fmt in time_formats:
            try:
                tupletime = time.strptime(strtime, fmt)
                itime = time.mktime(tupletime)
                return int(itime)
            except ValueError:
                continue
        
        # 如果所有格式都不匹配，抛出异常
        raise ValueError(f"时间格式不支持: {strtime}。支持的格式: YYYY-MM-DD HH:MM:SS, YYYY-MM-DD, YYYY/MM/DD HH:MM:SS, YYYY/MM/DD")
        
    def login(self) -> bool:
        """登录API"""
        if self.ver >= '5.4':
            page = "csras_api/login"
        else:
            page = "login.php"
            
        param = {"username": self.user, "password": self.password}
        try:
            resdata = self.request_data(page, param)
            js_obj = json.loads(resdata)
            if js_obj['login_errcode'] == 0:
                self.session = js_obj['session']
                return True
            else:
                return False
        except Exception:
            return False
        
    def logout(self):
        """登出"""
        if not self.session:
            return
            
        if self.ver >= '5.4':
            page = f"csras_api/{self.session}/logout/"
        else:
            page = f"logout.php?session={self.session}"
            
        try:
            self.request_data(page, '')
        except:
            pass

    def request_data(self, page: str, param: Any) -> bytes:
        """发送HTTP请求"""
        request_url = self.url + page
        if param:
            if isinstance(param, dict):
                param = json.dumps(param, sort_keys=False)
            param = 'param=' + urllib.parse.quote(param)
            req = urllib.request.Request(request_url, param.encode('utf-8'))
        else:
            req = urllib.request.Request(request_url, ''.encode('utf-8'))
        
        response = urllib.request.urlopen(req)
        return response.read()

    def get_api_data_status(self, data: bytes) -> int:
        """获取API响应状态码"""
        if data[0:1] == '{'.encode():
            try:
                js_obj = json.loads(data)
                return js_obj.get('errcode', -1)
            except Exception:
                return -1
        else:
            strcode = data[0:2]
            rndata = struct.unpack("!H", strcode)
            return rndata[0]

    def query_stats_data(self, table: str, begintime: str, endtime: str, 
                        fields: List[str], keys: List[str], timeunit: int = 0,
                        filter_condition: str = "", topcount: int = 1000,
                        sortfield: str = "total_byte", sorttype: int = 2,
                        netlink: int = 2, keycount: Optional[int] = None,
                        fieldcount: Optional[int] = None) -> Dict[str, Any]:
        """
        查询统计数据
        Args:
            table: 统计表名
            begintime: 开始时间
            endtime: 结束时间
            fields: 查询字段列表
            keys: 键字段列表
            timeunit: 时间单位
            filter_condition: 过滤条件
            topcount: 返回记录数
            sortfield: 排序字段
            sorttype: 排序类型
            netlink: 网络链路
            keycount: 键字段数量 (可选，通常设为None)
            fieldcount: 查询字段数量 (可选，通常设为None)
        """
        # 构建查询参数
        param = {
            "netlink": netlink,
            "table": table,
            "keys": keys,
            "fields": fields,
            "sorttype": sorttype,
            "sortfield": sortfield,
            "filter": filter_condition,
            "topcount": topcount,
            "keycount": keycount,
            "fieldcount": fieldcount,
            "begintime": self.strtime_to_inttime(begintime) * 1000,
            "endtime": self.strtime_to_inttime(endtime) * 1000,
            "timeunit": timeunit
        }
        
        # 执行查询
        if self.ver >= '5.4':
            page = f"csras_api/{self.session}/stats_data"
        else:
            page = f"statsquery.php?session={self.session}"
            
        resdata = self.request_data(page, param)
        
        # 检查响应状态
        return_code = self.get_api_data_status(resdata)
        
        if return_code != 0:
            error_message = self.get_error_message(return_code)
            return {
                "success": False,
                "error_code": return_code,
                "message": f"查询失败，错误码: {return_code} - {error_message}",
                "data": None
            }
        
        # 解析二进制数据为CSV格式
        try:
            # 首先尝试标准的二进制解析
            csv_data = self.parse_binary_to_csv(resdata, keys + fields)
            
            # 如果标准解析返回错误信息，尝试智能响应解析
            if csv_data.startswith("# 二进制解析错误") or csv_data.startswith("# 数据解析错误"):
                # 将二进制数据转换为字符串并尝试智能解析
                data_str = resdata.decode('utf-8', errors='ignore')
                csv_data = self.parse_response_data(data_str, keys + fields)
            
            return {
                "success": True,
                "error_code": 0,
                "message": "查询成功",
                "data": csv_data,
                "raw_data_size": len(resdata)
            }
        except Exception as e:
            return {
                "success": False,
                "error_code": -1,
                "message": f"数据解析失败: {str(e)}",
                "data": None,
                "raw_data_size": len(resdata)
            }

    def parse_binary_to_csv(self, data: bytes, columns: List[str]) -> str:
        """
        解析二进制数据为CSV格式 - 基于parse_api_data.py的完整实现
        Args:
            data: 二进制响应数据
            columns: 列名列表
        Returns:
            CSV格式字符串
        """
        try:
            offset = 0
            
            # 读取返回流中的错误码 (2字节)
            if len(data) < 2:
                return ",".join(columns)
            
            ret_code = struct.unpack("!H", data[offset:offset+2])[0]
            offset += 2
            
            if ret_code != 0:
                return f"# API错误码: {ret_code}\n" + ",".join(columns)
            
            # 读取错误信息的长度和错误信息内容 (4字节长度 + 内容)
            if len(data) < offset + 4:
                return ",".join(columns)
            
            err_msg_len = struct.unpack("!I", data[offset:offset+4])[0]
            offset += 4
            
            if len(data) < offset + err_msg_len:
                return ",".join(columns)
            
            err_msg = data[offset:offset+err_msg_len].decode('utf-8', errors='ignore')
            offset += err_msg_len
            
            # 解析所有表字段 - 字段数量 (2字节)
            if len(data) < offset + 2:
                return ",".join(columns)
            
            field_count = struct.unpack("!H", data[offset:offset+2])[0]
            offset += 2
            
            if field_count <= 0:
                return ",".join(columns)
            
            # 读取字段信息
            field_list = []
            for i in range(field_count):
                if len(data) < offset + 4:
                    break
                
                # 字段名长度 (4字节)
                name_len = struct.unpack("!I", data[offset:offset+4])[0]
                offset += 4
                
                if len(data) < offset + name_len + 1:
                    break
                
                # 字段名 (name_len字节)
                field_name = data[offset:offset+name_len].decode('utf-8', errors='ignore').strip('\x00')
                offset += name_len
                
                # 字段类型 (1字节)
                field_type = struct.unpack("!B", data[offset:offset+1])[0]
                offset += 1
                
                field_list.append({'name': field_name, 'type': field_type})
            
            # 解析链路数量 (1字节)
            if len(data) < offset + 1:
                return ",".join(columns)
            
            link_count = struct.unpack("!B", data[offset:offset+1])[0]
            offset += 1
            
            # 解析链路ID (2字节)
            if len(data) < offset + 2:
                return ",".join(columns)
            
            link_id = struct.unpack("!H", data[offset:offset+2])[0]
            offset += 2
            
            # 时间集个数 (4字节)
            if len(data) < offset + 4:
                return ",".join(columns)
            
            bucket_count = struct.unpack("!I", data[offset:offset+4])[0]
            offset += 4
            
            # 构建CSV数据
            csv_lines = []
            # 使用解析到的字段名作为标题
            field_names = [field['name'] for field in field_list]
            csv_lines.append(",".join(field_names))
            
            # 循环处理链路时间集
            for bucket_idx in range(bucket_count):
                if len(data) < offset + 8:
                    break
                
                # 解析链路记录集时间 (8字节时间戳)
                ticks = struct.unpack("!Q", data[offset:offset+8])[0]
                offset += 8
                str_time = self._convert_ticks_to_time(ticks / 1000)
                
                if len(data) < offset + 4:
                    break
                
                # 记录条数 (4字节)
                record_num = struct.unpack("!I", data[offset:offset+4])[0]
                offset += 4
                
                # 处理每条记录
                for rec_idx in range(record_num):
                    if offset >= len(data):
                        break
                    
                    row = []
                    
                    # 遍历每个字段，进行取值
                    for field in field_list:
                        try:
                            value = self._parse_field_by_type(data, offset, field['type'])
                            if isinstance(value, tuple):
                                field_value, new_offset = value
                                offset = new_offset
                            else:
                                field_value = value
                            
                            # 对于时间字段，使用解析到的时间
                            if field['name'].lower() == 'time' and field['type'] == 6:
                                row.append(str_time)
                            else:
                                row.append(str(field_value))
                                
                        except Exception as e:
                            row.append("")
                            break
                    
                    if len(row) == len(field_list) and any(row):
                        csv_lines.append(",".join(row))
            
            return "\n".join(csv_lines)
            
        except Exception as e:
            # 如果解析失败，尝试混合数据解析
            try:
                data_str = data.decode('utf-8', errors='ignore')
                if ',' in data_str:
                    return self.parse_mixed_data_to_csv(data_str, columns)
            except:
                pass
                
            error_info = f"# 二进制解析错误: {str(e)}\n"
            error_info += f"# 原始数据大小: {len(data)} 字节\n"
            error_info += f"# 数据预览: {data[:50].hex()}\n"
            return error_info + ",".join(columns)
    
    def _convert_ticks_to_time(self, ticks):
        """将时间戳转换为时间字符串"""
        try:
            return time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(ticks))
        except:
            return str(int(ticks))
    
    def _parse_field_by_type(self, data: bytes, offset: int, field_type: int):
        """根据字段类型解析数据"""
        try:
            # UINT8，1字节的无符号整数
            if field_type == 1:
                if offset + 1 > len(data):
                    return "", offset
                val = struct.unpack("!B", data[offset:offset+1])[0]
                return val, offset + 1
            
            # UINT16，2字节的无符号整数
            elif field_type == 2:
                if offset + 2 > len(data):
                    return "", offset
                val = struct.unpack("!H", data[offset:offset+2])[0]
                return val, offset + 2
            
            # UINT32，4字节的无符号整数
            elif field_type == 3:
                if offset + 4 > len(data):
                    return "", offset
                val = struct.unpack("!I", data[offset:offset+4])[0]
                return val, offset + 4
            
            # UINT64，8字节的无符号整数
            elif field_type == 4:
                if offset + 8 > len(data):
                    return "", offset
                val = struct.unpack("!Q", data[offset:offset+8])[0]
                return val, offset + 8
            
            # DOUBLE，8字节的浮点数
            elif field_type == 5:
                if offset + 8 > len(data):
                    return "", offset
                val = struct.unpack("!d", data[offset:offset+8])[0]
                return f"{val:.6f}", offset + 8
            
            # DATETIME，8字节的时间戳
            elif field_type == 6:
                if offset + 8 > len(data):
                    return "", offset
                ticks = struct.unpack("!Q", data[offset:offset+8])[0]
                str_time = self._convert_ticks_to_time(ticks / 1000)
                return str_time, offset + 8
            
            # TEXT，变长的文本字符串: length[4] + value[n]
            elif field_type == 7:
                if offset + 4 > len(data):
                    return "", offset
                text_len = struct.unpack("!I", data[offset:offset+4])[0]
                offset += 4
                if offset + text_len > len(data):
                    return "", offset
                text_val = data[offset:offset+text_len].decode('utf-8', errors='ignore')
                return text_val, offset + text_len
            
            # PERCENT，百分值，长度4字节
            elif field_type == 8:
                if offset + 4 > len(data):
                    return "", offset
                val = struct.unpack("!I", data[offset:offset+4])[0]
                percent = float(val) / 100
                return f"{percent}%", offset + 4
            
            # MAC，MAC地址，8个字节
            elif field_type == 9:
                if offset + 8 > len(data):
                    return "", offset
                mac_bytes = data[offset:offset+8]
                mac_str = ":".join([f"{b:02x}" for b in mac_bytes[:6]])
                return mac_str, offset + 8
            
            # IPADDR，由1字节的IP版本+n字节的IP地址组成
            elif field_type == 10:
                if offset + 1 > len(data):
                    return "", offset
                ip_ver = struct.unpack("!B", data[offset:offset+1])[0]
                offset += 1
                
                if ip_ver == 4:
                    if offset + 4 > len(data):
                        return "", offset
                    ip_bytes = data[offset:offset+4]
                    ip_str = ".".join([str(b) for b in ip_bytes])
                    return ip_str, offset + 4
                elif ip_ver == 6:
                    if offset + 16 > len(data):
                        return "", offset
                    ip_bytes = data[offset:offset+16]
                    # 简化IPv6显示
                    ip_str = ":".join([f"{ip_bytes[i]:02x}{ip_bytes[i+1]:02x}" for i in range(0, 16, 2)])
                    return ip_str, offset + 16
                else:
                    return "N/A", offset
            
            # 变长的请求或响应内容：length[4] + value[n]
            elif field_type == 11:
                if offset + 4 > len(data):
                    return "", offset
                content_len = struct.unpack("!I", data[offset:offset+4])[0]
                offset += 4
                if offset + content_len > len(data):
                    return "", offset
                content = data[offset:offset+content_len].decode('utf-8', errors='ignore')
                return content, offset + content_len
            
            # 虚拟ID, 如vlan id，1字节无符号整数
            elif field_type == 12:
                if offset + 1 > len(data):
                    return "", offset
                val = struct.unpack("!B", data[offset:offset+1])[0]
                return val, offset + 1
            
            else:
                return "", offset
                
        except Exception:
            return "", offset

    def parse_mixed_data_to_csv(self, raw_data: str, columns: List[str]) -> str:
        """
        专门处理API返回的混合格式数据（CSV头部+二进制数据）
        Args:
            raw_data: API返回的原始字符串数据
            columns: 期望的列名列表
        Returns:
            清理后的CSV格式字符串
        """
        try:
            # 清理数据：移除null字符和不可打印字符
            cleaned_data = ''.join(c if c.isprintable() or c in '\n\r\t,' else ' ' for c in raw_data)
            
            # 按行分割
            lines = cleaned_data.split('\n')
            csv_lines = []
            
            # 使用提供的列名作为标题
            csv_lines.append(",".join(columns))
            
            # 查找并处理数据行
            data_started = False
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                    
                # 跳过注释行和损坏的行
                if line.startswith('#') or line.startswith('"otal_bitps'):
                    continue
                
                # 检查是否是有效的数据行
                if ',' in line and not line.startswith('time,time'):
                    # 分割并清理字段
                    fields = [field.strip().replace('"', '') for field in line.split(',')]
                    
                    # 确保字段数量与列数匹配
                    if len(fields) >= len(columns):
                        fields = fields[:len(columns)]
                    elif len(fields) < len(columns):
                        fields.extend([''] * (len(columns) - len(fields)))
                    
                    # 验证和清理字段值
                    cleaned_fields = []
                    for i, field in enumerate(fields):
                        if field and field != 'total_byte':  # 避免重复的列名
                            # 尝试识别数值
                            try:
                                if '.' in field:
                                    float(field)
                                else:
                                    int(field)
                                cleaned_fields.append(field)
                            except:
                                # 如果不是数值，保留原值（可能是时间戳等）
                                cleaned_fields.append(field)
                        else:
                            cleaned_fields.append('')
                    
                    if any(cleaned_fields):  # 只添加非空行
                        csv_lines.append(",".join(cleaned_fields))
            
            return "\n".join(csv_lines)
            
        except Exception as e:
            return f"# 混合数据解析错误: {str(e)}\n" + ",".join(columns)

    def parse_response_data(self, raw_data: str, columns: List[str]) -> str:
        """
        智能解析响应数据 - 处理各种可能的数据格式
        Args:
            raw_data: API返回的原始字符串数据
            columns: 期望的列名列表
        Returns:
            清理后的CSV格式字符串
        """
        try:
            # 检查是否是空查询结果（以连续逗号结尾）
            if raw_data.endswith(',,,,,,,') or raw_data.endswith(',,,,,,,,'):
                # 返回只有标题行的CSV
                return ",".join(columns)
            
            # 检查是否包含CSV格式的数据
            if ',' in raw_data:
                # 按行分割数据
                lines = raw_data.split('\n')
                csv_lines = []
                
                # 使用提供的列名作为标题
                csv_lines.append(",".join(columns))
                
                # 查找有效的数据行
                for line in lines:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    
                    # 跳过损坏的行或标题行
                    if (line.startswith('time,time') or 
                        line.startswith('"otal_bitps') or
                        line == 'total_byte'):
                        continue
                    
                    # 检查是否是有效的数据行（包含逗号但不全是逗号）
                    if ',' in line and not line.replace(',', '').strip() == '':
                        # 清理字段
                        fields = [field.strip().replace('"', '').replace('\x00', '') 
                                for field in line.split(',')]
                        
                        # 过滤掉空的或无效的字段
                        if any(field for field in fields if field and field != ''):
                            # 确保字段数量匹配
                            while len(fields) < len(columns):
                                fields.append('')
                            if len(fields) > len(columns):
                                fields = fields[:len(columns)]
                            
                            csv_lines.append(",".join(fields))
                
                return "\n".join(csv_lines)
            
            # 如果没有逗号，可能是纯文本错误信息
            return f"# 数据格式错误: {raw_data[:100]}...\n" + ",".join(columns)
            
        except Exception as e:
            return f"# 响应解析错误: {str(e)}\n" + ",".join(columns)

# 全局API客户端实例
api_client = None

@mcp.tool()
def setup_api_connection(url: str) -> str:
    """
    设置API连接参数 (测试环境 - 自动使用默认测试账号，仅需提供服务器URL)
    
    Args:
        url: 统计API服务器的基础URL (例如: https://192.168.163.209:8080/)
    
    Returns:
        连接设置结果
    """
    global api_client
    
    try:
        if not url:
            return "错误：缺少必需的参数 url"

        if not url.endswith('/'):
            url = url + '/'
        
        # 忽略SSL证书验证
        ssl._create_default_https_context = ssl._create_unverified_context
        
        # 创建测试客户端实例，使用默认测试账号
        api_client = StatsApiClient(url, "admin", "D&^4Vs!(", "6.1")
        
        # 尝试登录并提供详细的错误信息
        try:
            if api_client.login():
                return f"API连接设置成功，已登录到 {url}"
            else:
                # 尝试获取更详细的登录错误信息
                try:
                    page = "csras_api/login"
                    param = {"username": "admin", "password": "D&^4Vs!"}
                    resdata = api_client.request_data(page, param)
                    js_obj = json.loads(resdata)
                    error_code = js_obj.get('login_errcode', -1)
                    error_message = api_client.get_error_message(error_code)
                    return f"API登录失败: 错误码 {error_code} - {error_message}"
                except Exception as e:
                    print(e)
                    return "API登录失败: 用户名或密码错误，或服务器连接问题"
        except Exception as login_error:
            return f"API登录过程中出错: {str(login_error)}"
        
    except Exception as e:
        return f"API连接设置失败: {str(e)}"

def get_default_keys(table: str) -> List[str]:
    """
    获取默认键字段列表，建议使用list_table_fields函数获取准确的字段信息
    """
    # TODO: 可以调用list_table_fields获取表的key字段
    return []

def validate_table_fields(table_name: str, fields: List[str], keys: List[str]) -> Dict[str, Any]:
    """
    验证表字段是否存在，过滤无效字段
    
    Args:
        table_name: 表名
        fields: 要验证的字段列表
        keys: 要验证的key字段列表
    
    Returns:
        包含验证结果和有效字段的字典
    """
    global api_client
    
    if api_client is None:
        return {
            "valid_fields": [],
            "valid_keys": [],
            "errors": ["API未连接"]
        }
    
    try:
        # 获取表的所有字段信息
        url = f"csras_api/{api_client.session}/tables/{table_name}"
        resdata = api_client.request_data(url, '')
        js_obj = json.loads(resdata)
        
        if js_obj.get('errcode', 0) != 0:
            return {
                "valid_fields": [],
                "valid_keys": [],
                "errors": [f"获取表字段失败: {js_obj.get('errmsg', '未知错误')}"]
            }
        
        # 解析字段信息
        table_fields = js_obj.get('fields', [])
        available_fields = {field['name']: field for field in table_fields}
        available_key_fields = {field['name']: field for field in table_fields if field.get('key', 0) == 1}
        available_value_fields = {field['name']: field for field in table_fields if field.get('key', 0) == 0}
        
        # 验证fields
        valid_fields = []
        invalid_fields = []
        for field in fields:
            if field == "*":  # 特殊处理通配符
                # 添加所有可用字段
                valid_fields.extend(available_fields.keys())
                break
            elif field in available_fields:
                valid_fields.append(field)
            else:
                invalid_fields.append(field)
        
        # 验证keys
        valid_keys = []
        invalid_keys = []
        for key in keys:
            if key == "*":  # 特殊处理通配符
                # 添加所有key字段
                valid_keys.extend(available_key_fields.keys())
                break
            elif key in available_key_fields:
                valid_keys.append(key)
            else:
                invalid_keys.append(key)
        
        # 如果没有有效的key字段，使用默认key字段
        if not valid_keys and available_key_fields:
            valid_keys = list(available_key_fields.keys())
        
        # 如果没有有效的fields，使用默认value字段
        if not valid_fields and available_value_fields:
            valid_fields = list(available_value_fields.keys())[:5]  # 限制前5个字段避免过多
        
        # 去重
        valid_fields = list(dict.fromkeys(valid_fields))  # 保持顺序去重
        valid_keys = list(dict.fromkeys(valid_keys))
        
        errors = []
        if invalid_fields:
            errors.append(f"无效的fields字段: {', '.join(invalid_fields)}")
        if invalid_keys:
            errors.append(f"无效的keys字段: {', '.join(invalid_keys)}")
        
        return {
            "valid_fields": valid_fields,
            "valid_keys": valid_keys,
            "invalid_fields": invalid_fields,
            "invalid_keys": invalid_keys,
            "errors": errors,
            "available_fields": list(available_fields.keys()),
            "available_key_fields": list(available_key_fields.keys()),
            "available_value_fields": list(available_value_fields.keys())
        }
        
    except Exception as e:
        return {
            "valid_fields": [],
            "valid_keys": [],
            "errors": [f"字段验证失败: {str(e)}"]
        }

@mcp.tool()
def query_statistics_table(
    table: str,
    begintime: str,
    endtime: str,
    fields: str,
    keys: str,
    timeunit: int = 0,
    filter_condition: str = "",
    topcount: int = 1000,
    sortfield: str = "total_byte",
    sorttype: int = 2,
    netlink: int = 2,
) -> str:
    """
    查询统计表数据
    
    Args:
        table: 统计表名 (可使用获取表获取所有表)
        begintime: 开始时间 (格式: YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD)
        endtime: 结束时间 (格式: YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD)
        fields: 查询字段列表 (例如: "server_ip_addr,total_byte,protocol" 或 "*" 表示所有字段，可使用获取表字段获取表的所有字段)
        keys: 键字段列表 (例如: "server_ip_addr,server_port" 或 "*" 表示所有key字段或空字符串使用默认Key，可使用获取表字段获取表的key字段)
        timeunit: 时间单位 默认0 (0=不按照时间维度合并, 1000=秒级+key, 10000=10s级+key, 60000=分钟级+key, 3600000=小时级+key, 86400000=天级+key)
        filter_condition: 过滤条件 (例如: "total_byte>1000", 过滤条件的字段为统计表里面的字段)
        topcount: 返回记录数 (默认: 1000)
        sortfield: 排序字段 (默认: total_byte)
        sorttype: 排序类型 (1=升序, 2=降序)
        netlink: 要查询的网络链路ID (默认: 2)
    
    Returns:
        查询结果的JSON字符串
    """
    global api_client
    
    if api_client is None:
        result = {
            "success": False,
            "message": "请先使用 setup_api_connection 设置API连接"
        }
        return json.dumps(result, ensure_ascii=False, indent=2)
    
    try:
        # 处理fields参数：支持字符串和列表格式
        if isinstance(fields, str):
            if fields.strip():
                fields_list = [field.strip() for field in fields.split(',') if field.strip()]
            else:
                fields_list = []
        else:
            fields_list = fields if fields else []
        
        # 处理keys参数：支持字符串和列表格式
        if isinstance(keys, str):
            if keys.strip():
                keys_list = [key.strip() for key in keys.split(',') if key.strip()]
            else:
                keys_list = []
        else:
            keys_list = keys if keys else []
        
        # 验证字段
        validation_result = validate_table_fields(table, fields_list, keys_list)
        
        if validation_result["errors"] and not validation_result["valid_fields"] and not validation_result["valid_keys"]:
            result = {
                "success": False,
                "message": f"字段验证失败: {'; '.join(validation_result['errors'])}",
                "validation_errors": validation_result["errors"]
            }
            return json.dumps(result, ensure_ascii=False, indent=2)
        
        # 使用验证后的有效字段
        valid_fields = validation_result["valid_fields"]
        valid_keys = validation_result["valid_keys"]
        
        if not all([table, begintime, endtime, valid_fields, valid_keys]):
            result = {
                "success": False,
                "message": "缺少必需的参数或字段验证失败",
                "details": {
                    "table": table,
                    "begintime": begintime,
                    "endtime": endtime,
                    "valid_fields_count": len(valid_fields),
                    "valid_keys_count": len(valid_keys)
                },
                "field_validation": validation_result
            }
            return json.dumps(result, ensure_ascii=False, indent=2)
        
        result = api_client.query_stats_data(
            table=table,
            begintime=begintime,
            endtime=endtime,
            fields=valid_fields,
            keys=valid_keys,
            timeunit=timeunit,
            filter_condition=filter_condition,
            topcount=topcount,
            sortfield=sortfield,
            sorttype=sorttype,
            netlink=netlink,
            keycount=None,
            fieldcount=None
        )
        
        # 添加查询参数信息和字段验证信息
        result["query_info"] = {
            "table": table,
            "time_range": f"{begintime} 到 {endtime}",
            "fields_count": len(valid_fields),
            "keys_count": len(valid_keys),
            "filter": filter_condition or "无",
            "topcount": topcount,
            "keycount": None,
            "fieldcount": None
        }
        
        # 添加字段验证信息
        if validation_result["errors"]:
            result["field_validation_warnings"] = validation_result["errors"]
        
        if validation_result.get("invalid_fields") or validation_result.get("invalid_keys"):
            result["field_validation_info"] = {
                "original_fields": fields_list,
                "original_keys": keys_list,
                "valid_fields": valid_fields,
                "valid_keys": valid_keys,
                "invalid_fields": validation_result.get("invalid_fields", []),
                "invalid_keys": validation_result.get("invalid_keys", [])
            }
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        result = {
            "success": False,
            "message": f"查询失败: {str(e)}",
            "traceback": traceback.format_exc()
        }
        return json.dumps(result, ensure_ascii=False, indent=2)

@mcp.tool()
def disconnect_api() -> str:
    """
    断开API连接
    
    Returns:
        断开连接结果
    """
    global api_client
    
    if api_client is None:
        return "没有活动的API连接"
    
    try:
        api_client.logout()
        api_client = None
        return "API连接已断开"
    except Exception as e:
        return f"断开连接时出错: {str(e)}"

@mcp.tool()
def get_time_examples() -> str:
    """
    获取时间格式和timeunit参数的说明
    
    Returns:
        时间参数说明的JSON字符串
    """
    time_info = {
        "supported_time_formats": {
            "description": "支持的时间格式列表",
            "formats": [
                {
                    "format": "YYYY-MM-DD HH:MM:SS",
                    "description": "完整时间格式",
                    "examples": ["2025-06-01 00:00:00", "2025-06-03 23:59:59", "2025-01-15 14:30:00"]
                },
                {
                    "format": "YYYY-MM-DD",
                    "description": "日期格式（自动补充00:00:00）",
                    "examples": ["2025-06-01", "2025-06-03", "2025-01-15"]
                },
                {
                    "format": "YYYY/MM/DD HH:MM:SS",
                    "description": "斜杠分隔的完整时间格式",
                    "examples": ["2025/06/01 00:00:00", "2025/06/03 23:59:59"]
                },
                {
                    "format": "YYYY/MM/DD",
                    "description": "斜杠分隔的日期格式",
                    "examples": ["2025/06/01", "2025/06/03"]
                }
            ]
        },
        "field_parameters": {
            "description": "字段参数说明",
            "fields": {
                "description": "查询字段，支持多种格式",
                "formats": [
                    "具体字段名: 'server_ip_addr,total_byte,protocol'",
                    "通配符: '*' 表示查询所有可用字段",
                    "空字符串: '' 使用默认字段"
                ]
            },
            "keys": {
                "description": "键字段，支持多种格式",
                "formats": [
                    "具体键名: 'server_ip_addr,server_port'", 
                    "通配符: '*' 表示使用所有可用的key字段",
                    "空字符串: '' 使用默认key字段"
                ]
            }
        },
        "timeunit_values": {
            "0": "不按照时间维度合并，即为查询时间按照key进行合并统计",
            "1000": "按照秒级和Key组合统计", 
            "10000": "按照10s级和Key组合统计",
            "60000": "按照分钟级和Key组合统计",
            "3600000": "按照小时级和Key组合统计",
            "86400000": "按照天级和Key组合统计"
        },
        "filter_examples": [
            "total_byte>1000000",
            "server_port=80",
            "total_packet>1000",
            "visit_count>10"
        ],
        "sorttype_values": {
            "1": "升序排列",
            "2": "降序排列"
        }
    }
    
    return json.dumps(time_info, ensure_ascii=False, indent=2)

@mcp.tool()
def get_api_error_codes() -> str:
    """
    获取所有API错误码及其中文描述
    
    Returns:
        所有错误码及描述的JSON字符串
    """
    error_codes = {
        "api_error_codes": {
            "0": "请求成功",
            "1": "资源错误",
            "2": "创建线程失败", 
            "3": "未知命令",
            "4": "未实现",
            "5": "需要登录",
            "6": "没有权限",
            "7": "太多请求",
            "8": "用户取消",
            "9": "参数错误",
            "10": "连接已断开",
            "11": "请求超时",
            "13": "参数错误：查询字段不能解析",
            "14": "参数错误：无效的查询表名称", 
            "15": "参数错误：无效的链路ID",
            "16": "参数错误：无效的查询时间",
            "17": "参数错误：无效的时间单位",
            "18": "参数错误：无效的排序类型",
            "19": "参数错误：无效的时间周期",
            "20": "参数错误：无效的配置类型",
            "21": "查询表不存在",
            "22": "查询字段不存在",
            "23": "请求失败",
            "24": "系统繁忙",
            "25": "错误的URL",
            "26": "参数错误",
            "255": "其他错误"
        },
        "common_error_solutions": {
            "5": "请先调用 setup_api_connection 设置连接",
            "14": "请检查表名是否正确，可使用 list_statistics_tables 查看所有可用表名",
            "16": "请检查时间格式是否为 YYYY-MM-DD HH:MM:SS",
            "17": "请检查 timeunit 参数值，可使用 get_time_examples 查看有效值",
            "21": "确认统计表是否存在，使用 list_statistics_tables 检查表名拼写",
            "22": "确认查询字段名是否正确，使用 list_table_fields 检查 fields 和 keys 参数",
            "26": "检查所有参数是否有效，特别是时间范围、字段名、表名等"
        }
    }
    
    return json.dumps(error_codes, ensure_ascii=False, indent=2)

# @mcp.tool()
# def get_detailed_packet_decode(
#     begin_time: str,
#     end_time: str, 
#     server_ip: str,
#     client_ip: str = "",
#     protocol: str = "TCP",
#     link_id: int = 2,
#     max_packets: int = 1000,
#     decode_options: str = "basic"
# ) -> str:
#     """
#     获取指定时间范围内的数据包详细解码信息 (基于11.3接口规范)
    
#     Args:
#         begin_time: 开始时间 (格式: YYYY-MM-DD HH:MM:SS)
#         end_time: 结束时间 (格式: YYYY-MM-DD HH:MM:SS)
#         server_ip: 服务器IP地址
#         client_ip: 客户端IP地址 (可选，为空则查询所有客户端)
#         protocol: 协议类型 (TCP/UDP，默认TCP)
#         link_id: 链路ID (默认2)
#         max_packets: 最大返回包数量 (默认1000)
#         decode_options: 解码选项 (basic/detailed/full，默认basic)
    
#     Returns:
#         包详细解码信息的JSON字符串，包含序列号、确认号、标志位等详细信息
#     """
#     global api_client
    
#     if api_client is None:
#         result = {
#             "success": False,
#             "message": "请先使用 setup_api_connection 设置API连接"
#         }
#         return json.dumps(result, ensure_ascii=False, indent=2)
    
#     try:
#         # 构建数据包解码查询参数
#         decode_params = {
#             "begin_time": begin_time,
#             "end_time": end_time,
#             "server_ip": server_ip,
#             "client_ip": client_ip,
#             "protocol": protocol.upper(),
#             "link_id": link_id,
#             "max_packets": max_packets,
#             "decode_level": decode_options
#         }
        
#         # 模拟数据包解码API调用（实际环境中应调用真实的解码接口）
#         # 这里生成模拟的数据包解码数据用于演示
#         packet_data = generate_mock_packet_decode_data(decode_params)
        
#         result = {
#             "success": True,
#             "message": "数据包解码成功",
#             "decode_params": decode_params,
#             "packet_count": len(packet_data.get("packets", [])),
#             "data": packet_data
#         }
        
#         return json.dumps(result, ensure_ascii=False, indent=2)
        
#     except Exception as e:
#         result = {
#             "success": False,
#             "message": f"数据包解码失败: {str(e)}",
#             "traceback": traceback.format_exc()
#         }
#         return json.dumps(result, ensure_ascii=False, indent=2)

@mcp.tool()
def get_config(config_type: str, netlink_id: int = 1) -> str:
    """
    获取系统配置信息
    
    Args:
        config_type: 配置类型 (支持的类型包括: subNetlink, customprotocol, bargain, statsegment, 
                     nametable, alarminfo, application, innersegment, metafetch, netlink, 
                     account, storage, adapter, querytask, broker, serverconfig, fieldclip, 
                     netflowdevinfo)
        netlink_id: 链路ID (对于链路配置使用，系统配置固定为64)
    
    Returns:
        配置信息的JSON字符串，包含XML格式的配置内容
    """
    global api_client
    
    if api_client is None:
        result = {
            "success": False,
            "message": "请先使用 setup_api_connection 设置API连接"
        }
        return json.dumps(result, ensure_ascii=False, indent=2)
    
    # 系统配置类型
    system_configs = ["netlink", "account", "storage", "adapter", "querytask", 
                     "broker", "serverconfig", "fieldclip", "netflowdevinfo"]
    
    # 判断是否为系统配置
    if config_type in system_configs:
        url = f"csras_api/{api_client.session}/configs/getting/{config_type}/"
    else:
        url = f"csras_api/{api_client.session}/configs/getting/{config_type}/{netlink_id}"
    
    try:
        resdata = api_client.request_data(url, '')
        js_obj = json.loads(resdata)
        
        if js_obj.get('errcode', 0) == 0:
            result = {
                "success": True,
                "message": "配置获取成功",
                "config_type": config_type,
                "netlink_id": 64 if config_type in system_configs else netlink_id,
                "xml": js_obj.get('xml', '')
            }
        else:
            result = {
                "success": False,
                "error_code": js_obj.get('errcode'),
                "message": js_obj.get('errmsg', '配置获取失败')
            }
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        result = {
            "success": False,
            "message": f"配置获取失败: {str(e)}",
            "traceback": traceback.format_exc()
        }
        return json.dumps(result, ensure_ascii=False, indent=2)

# @mcp.tool()
# def set_config(config_type: str, xml_config: str, netlink_id: int = 1, child_type: str = "") -> str:
#     """
#     设置系统配置 (基于7.2配置接口说明)
    
#     Args:
#         config_type: 配置类型 (同get_config支持的类型)
#         xml_config: XML格式的配置内容
#         netlink_id: 链路ID (对于链路配置使用)
#         child_type: 子类型 (通常留空)
    
#     Returns:
#         配置设置结果的JSON字符串
#     """
#     global api_client
    
#     if api_client is None:
#         result = {
#             "success": False,
#             "message": "请先使用 setup_api_connection 设置API连接"
#         }
#         return json.dumps(result, ensure_ascii=False, indent=2)
    
#     url = f"csras_api/{api_client.session}/configs/setting"
    
#     param = {
#         "type": config_type,
#         "child_type": child_type,
#         "netlink": netlink_id,
#         "xml": xml_config
#     }
    
#     try:
#         resdata = api_client.request_data(url, param)
#         js_obj = json.loads(resdata)
        
#         if js_obj.get('errcode', 0) == 0:
#             result = {
#                 "success": True,
#                 "message": "配置设置成功",
#                 "config_type": config_type,
#                 "netlink_id": netlink_id,
#                 "response_xml": js_obj.get('xml', '')
#             }
#         else:
#             result = {
#                 "success": False,
#                 "error_code": js_obj.get('errcode'),
#                 "message": js_obj.get('errmsg', '配置设置失败'),
#                 "response_xml": js_obj.get('xml', '')
#             }
        
#         return json.dumps(result, ensure_ascii=False, indent=2)
        
#     except Exception as e:
#         result = {
#             "success": False,
#             "message": f"配置设置失败: {str(e)}",
#             "traceback": traceback.format_exc()
#         }
#         return json.dumps(result, ensure_ascii=False, indent=2)

@mcp.tool()
def list_statistics_tables() -> str:
    """
    枚举系统中支持的统计表，用于确定query_statistics_table的table参数
    
    Returns:
        统计表列表的JSON字符串，包含表名和描述
    """
    global api_client
    
    if api_client is None:
        result = {
            "success": False,
            "message": "请先使用 setup_api_connection 设置API连接"
        }
        return json.dumps(result, ensure_ascii=False, indent=2)
    
    url = f"csras_api/{api_client.session}/tables"
    
    try:
        resdata = api_client.request_data(url, '')
        js_obj = json.loads(resdata)
        
        if js_obj.get('errcode', 0) == 0:
            result = {
                "success": True,
                "message": "统计表枚举成功",
                "table_count": len(js_obj.get('tables', [])),
                "tables": js_obj.get('tables', [])
            }
        else:
            result = {
                "success": False,
                "error_code": js_obj.get('errcode'),
                "message": js_obj.get('errmsg', '统计表枚举失败')
            }
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        result = {
            "success": False,
            "message": f"统计表枚举失败: {str(e)}",
            "traceback": traceback.format_exc()
        }
        return json.dumps(result, ensure_ascii=False, indent=2)

@mcp.tool()
def list_table_fields(table_name: str) -> str:
    """
    枚举指定统计表的字段信息，用于确定query_statistics_table的fields和keys参数
    
    Args:
        table_name: 统计表名称 (例如: tcp_flow, ip_flow, summary等，可先使用list_statistics_tables获取表名)
    
    Returns:
        字段列表的JSON字符串，包含字段名、类型、是否为key等信息
        返回的fields数组中，key=true的字段可用作keys参数，所有字段都可用作fields参数
    """
    global api_client
    
    if api_client is None:
        result = {
            "success": False,
            "message": "请先使用 setup_api_connection 设置API连接"
        }
        return json.dumps(result, ensure_ascii=False, indent=2)
    
    url = f"csras_api/{api_client.session}/tables/{table_name}"
    
    try:
        resdata = api_client.request_data(url, '')
        js_obj = json.loads(resdata)
        
        if js_obj.get('errcode', 0) == 0:
            # 整理字段信息为精简格式
            fields = js_obj.get('fields', [])
            
            # 分离key字段和value字段
            key_fields = {}
            value_fields = {}
            
            for field in fields:
                field_name = field.get('name', '')
                field_caption = field.get('caption', field_name)
                
                if field.get('key', 0) == 1:  # key字段
                    key_fields[field_name] = field_caption
                else:  # value字段
                    value_fields[field_name] = field_caption
            
            result = {
                "success": True,
                "message": "字段枚举成功",
                "table_name": table_name,
                "key": key_fields,
                "value": value_fields
            }
        else:
            result = {
                "success": False,
                "error_code": js_obj.get('errcode'),
                "message": js_obj.get('errmsg', '字段枚举失败')
            }
        
        return json.dumps(result, ensure_ascii=False, indent=2)
        
    except Exception as e:
        result = {
            "success": False,
            "message": f"字段枚举失败: {str(e)}",
            "traceback": traceback.format_exc()
        }
        return json.dumps(result, ensure_ascii=False, indent=2)

def get_field_type_description(field_type: int) -> str:
    """获取字段类型的中文描述"""
    type_map = {
        1: "UINT8 (1字节无符号整数)",
        2: "UINT16 (2字节无符号整数)",
        3: "UINT32 (4字节无符号整数)",
        4: "UINT64 (8字节无符号整数)",
        5: "DOUBLE (8字节浮点数)",
        6: "DATETIME (8字节时间戳)",
        7: "TEXT (变长文本字符串)",
        8: "PERCENT (百分值)",
        9: "MAC (MAC地址)",
        10: "IPADDR (IP地址)",
        11: "交易内容 (变长内容)",
        12: "虚拟ID (如VLAN ID)"
    }
    return type_map.get(field_type, f"未知类型({field_type})")

# @mcp.tool()
# def download_packets(
#     netlink_id: int,
#     begin_time: str,
#     end_time: str,
#     filter_condition: str = "",
#     output_format: str = "summary"
# ) -> str:
#     """
#     下载指定时间段的数据包 (基于10.2下载数据包接口)
    
#     Args:
#         netlink_id: 链路ID
#         begin_time: 开始时间 (格式: YYYY-MM-DD HH:MM:SS)
#         end_time: 结束时间 (格式: YYYY-MM-DD HH:MM:SS)
#         filter_condition: 过滤条件 (例如: "ip_addr=***********", "port=80", "ip_flow=[***********]-[*********]")
#         output_format: 输出格式 (summary=摘要信息, detail=详细信息)
    
#     Returns:
#         数据包信息的JSON字符串
#     """
#     global api_client
    
#     if api_client is None:
#         result = {
#             "success": False,
#             "message": "请先使用 setup_api_connection 设置API连接"
#         }
#         return json.dumps(result, ensure_ascii=False, indent=2)
    
#     url = f"csras_api/{api_client.session}/data_packets"
    
#     # 转换时间为时间戳（秒）
#     begin_timestamp = api_client.strtime_to_inttime(begin_time)
#     end_timestamp = api_client.strtime_to_inttime(end_time)
    
#     param = {
#         "netlink": netlink_id,
#         "begintime": begin_timestamp,
#         "endtime": end_timestamp,
#         "filter": filter_condition
#     }
    
#     try:
#         resdata = api_client.request_data(url, param)
        
#         # 检查错误码（前2字节）
#         if len(resdata) >= 2:
#             error_code = struct.unpack("!H", resdata[0:2])[0]
            
#             if error_code != 0:
#                 # 读取错误信息
#                 if len(resdata) >= 6:
#                     msg_len = struct.unpack("!I", resdata[2:6])[0]
#                     if len(resdata) >= 6 + msg_len:
#                         error_msg = resdata[6:6+msg_len].decode('utf-8', errors='ignore')
#                     else:
#                         error_msg = f"错误码: {error_code}"
#                 else:
#                     error_msg = f"错误码: {error_code}"
                
#                 result = {
#                     "success": False,
#                     "error_code": error_code,
#                     "message": f"数据包下载失败: {error_msg}"
#                 }
#             else:
#                 # 解析数据包信息
#                 packet_info = parse_packet_data(resdata[2:], output_format)
#                 result = {
#                     "success": True,
#                     "message": "数据包下载成功",
#                     "query_params": {
#                         "netlink_id": netlink_id,
#                         "time_range": f"{begin_time} 到 {end_time}",
#                         "filter": filter_condition or "无"
#                     },
#                     "packet_info": packet_info
#                 }
#         else:
#             result = {
#                 "success": False,
#                 "message": "返回数据格式错误"
#             }
        
#         return json.dumps(result, ensure_ascii=False, indent=2)
        
#     except Exception as e:
#         result = {
#             "success": False,
#             "message": f"数据包下载失败: {str(e)}",
#             "traceback": traceback.format_exc()
#         }
#         return json.dumps(result, ensure_ascii=False, indent=2)

def parse_packet_data(data: bytes, output_format: str) -> Dict[str, Any]:
    """解析数据包二进制数据"""
    if output_format == "summary":
        # 返回摘要信息
        return {
            "data_size": len(data),
            "format": "二进制数据包格式",
            "note": "实际环境中会包含数据包的详细信息"
        }
    else:
        # 这里应该实现完整的数据包解析逻辑
        # 暂时返回模拟数据
        return {
            "data_size": len(data),
            "packet_count": random.randint(10, 100),
            "packets_preview": [
                {
                    "id": 1,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3],
                    "size": 1500,
                    "protocol": "TCP"
                }
            ]
        }

# @mcp.tool()
# def get_packet_summary_decode(
#     netlink_id: int,
#     begin_time: str,
#     end_time: str,
#     filter_condition: str = "",
#     max_bytes: int = 10000,
#     max_packets: int = 100
# ) -> str:
#     """
#     获取数据包概要解码信息 (基于11.2数据包概要解码接口)
    
#     Args:
#         netlink_id: 链路ID
#         begin_time: 开始时间 (格式: YYYY-MM-DD HH:MM:SS)
#         end_time: 结束时间 (格式: YYYY-MM-DD HH:MM:SS)
#         filter_condition: 过滤条件 (例如: "ip_addr=***********")
#         max_bytes: 总字节数限制 (默认10000)
#         max_packets: 总数据包限制 (默认100)
    
#     Returns:
#         数据包概要解码信息的JSON字符串
#     """
#     global api_client
    
#     if api_client is None:
#         result = {
#             "success": False,
#             "message": "请先使用 setup_api_connection 设置API连接"
#         }
#         return json.dumps(result, ensure_ascii=False, indent=2)
    
#     url = f"csras_api/{api_client.session}/forward"
    
#     # 转换时间为时间戳（秒）
#     begin_timestamp = api_client.strtime_to_inttime(begin_time)
#     end_timestamp = api_client.strtime_to_inttime(end_time)
    
#     param = {
#         "cmd": 125,  # 概要解码命令
#         "fmt": 1,
#         "list": [
#             {"type": "uint16_t", "value": netlink_id},
#             {"type": "uint32_t", "value": begin_timestamp},
#             {"type": "uint32_t", "value": end_timestamp},
#             {"type": "string", "value": filter_condition},
#             {"type": "uint32_t", "value": max_bytes},
#             {"type": "uint32_t", "value": max_packets}
#         ]
#     }
    
#     try:
#         resdata = api_client.request_data(url, param)
        
#         # 尝试解析JSON响应
#         try:
#             response_data = json.loads(resdata)
            
#             # 检查是否有错误
#             if isinstance(response_data, dict) and response_data.get('errcode'):
#                 result = {
#                     "success": False,
#                     "error_code": response_data.get('errcode'),
#                     "message": response_data.get('errmsg', '数据包解码失败')
#                 }
#             else:
#                 # 成功获取解码数据
#                 result = {
#                     "success": True,
#                     "message": "数据包概要解码成功",
#                     "query_params": {
#                         "netlink_id": netlink_id,
#                         "time_range": f"{begin_time} 到 {end_time}",
#                         "filter": filter_condition or "无",
#                         "max_bytes": max_bytes,
#                         "max_packets": max_packets
#                     },
#                     "packet_count": len(response_data) if isinstance(response_data, list) else 1,
#                     "packets": response_data
#                 }
#         except json.JSONDecodeError:
#             # 如果不是JSON格式，可能是二进制数据
#             result = {
#                 "success": False,
#                 "message": "响应数据格式错误，预期为JSON格式",
#                 "raw_data_size": len(resdata)
#             }
        
#         return json.dumps(result, ensure_ascii=False, indent=2)
        
#     except Exception as e:
#         result = {
#             "success": False,
#             "message": f"数据包概要解码失败: {str(e)}",
#             "traceback": traceback.format_exc()
#         }
#         return json.dumps(result, ensure_ascii=False, indent=2)

@mcp.tool()
def get_supported_filters() -> str:
    """
    获取支持的过滤器对象和使用说明
    
    Returns:
        过滤器说明的JSON字符串
    """
    filter_info = {
        "download_filters": {
            "ip_addr": {
                "description": "IP地址过滤",
                "example": "ip_addr=***********"
            },
            "ip_range": {
                "description": "IP范围过滤",
                "example": "ip_range=***********-*************"
            },
            "ip_flow": {
                "description": "IP会话过滤",
                "example": "ip_flow=[***********]-[*********]"
            },
            "ipport_flow": {
                "description": "TCP/UDP会话过滤",
                "example": "ipport_flow=[*************]:5652-[*************]:443"
            },
            "port": {
                "description": "端口过滤",
                "example": "port=443"
            },
            "port_range": {
                "description": "端口范围过滤",
                "example": "port_range=80-443"
            },
            "phys_addr": {
                "description": "物理地址过滤",
                "example": "phys_addr=1C:6F:65:97:20:66"
            },
            "application": {
                "description": "应用过滤（需要应用ID）",
                "example": "application=30001"
            },
            "vlan_id": {
                "description": "VLAN ID过滤",
                "example": "vlan_id=236"
            },
            "protocol": {
                "description": "协议过滤",
                "example": "top_protocol=600"
            }
        },
        "query_filters": {
            "description": "查询过滤器支持字段比较和逻辑运算",
            "operators": {
                "=": "等于",
                "!=": "不等于",
                ">=": "大于等于",
                "<=": "小于等于",
                ">": "大于",
                "<": "小于"
            },
            "logical_operators": {
                "&&": "与关系",
                "||": "或关系"
            },
            "examples": [
                "total_byte>1000000",
                "server_port=80",
                "((server_ip_addr=***************)&&(server_port=80))",
                "total_packet>1000||total_byte>1000000"
            ],
            "special_chars": {
                "&": "需要转义为%26"
            }
        }
    }
    
    return json.dumps(filter_info, ensure_ascii=False, indent=2)

@mcp.tool()
def get_config_types() -> str:
    """
    获取支持的配置类型说明
    
    Returns:
        配置类型说明的JSON字符串
    """
    config_types = {
        "link_configs": {
            "description": "链路配置类型",
            "types": {
                "subNetlink": "子链路配置",
                "customprotocol": "自定义协议",
                "bargain": "交易配置",
                "statsegment": "网段配置",
                "nametable": "名字表配置",
                "alarminfo": "警报配置",
                "application": "应用配置",
                "innersegment": "进出网网段配置",
                "metafetch": "元数据提取任务"
            }
        },
        "system_configs": {
            "description": "系统配置类型（netlink_id固定为64）",
            "types": {
                "netlink": "链路配置",
                "account": "账户配置",
                "storage": "存储配置",
                "adapter": "接口配置",
                "querytask": "推送任务配置",
                "broker": "Broker配置",
                "serverconfig": "系统配置",
                "fieldclip": "字段裁剪配置",
                "netflowdevinfo": "Netflow设备配置"
            }
        }
    }
    
    return json.dumps(config_types, ensure_ascii=False, indent=2)

if __name__ == '__main__':
    mcp.run() 