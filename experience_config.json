{"experience_manager": {"enabled": true, "experience_file": "react_experiences.json", "embedding_model": "embedding-3", "similarity_threshold": 0.7, "max_experiences": 1000, "auto_save": true, "use_embedding": true, "top_k_similar": 3, "min_execution_time": 1.0, "min_token_usage": 50}, "embedding_config": {"provider": "openai", "model": "embedding-3", "base_url": "https://open.bigmodel.cn/api/paas/v4/embeddings", "api_key": "f0a243a954a159c3da8d442db806df3a.4qtTWbA1ANGEGF0E", "batch_size": 32, "timeout": 30}, "text_similarity_config": {"jaccard_weight": 0.5, "char_weight": 0.3, "length_weight": 0.2, "min_similarity": 0.1}, "storage_config": {"backup_enabled": true, "backup_interval_hours": 24, "max_backup_files": 5, "compression_enabled": false}, "logging_config": {"log_level": "INFO", "log_file": "experience_manager.log", "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "max_log_size_mb": 10, "backup_count": 3}}